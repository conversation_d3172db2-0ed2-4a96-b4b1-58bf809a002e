import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/trial.dart';
import '../models/user.dart';

class StorageService {
  static const String _trialsBoxName = 'trials';
  static const String _userBoxName = 'user';
  static const String _settingsBoxName = 'settings';
  
  static const String _currentUserIdKey = 'current_user_id';
  static const String _isFirstLaunchKey = 'is_first_launch';
  static const String _onboardingCompletedKey = 'onboarding_completed';

  static Box<Trial>? _trialsBox;
  static Box<User>? _userBox;
  static SharedPreferences? _prefs;

  static Future<void> init() async {
    await Hive.initFlutter();
    
    // Register adapters
    Hive.registerAdapter(TrialAdapter());
    Hive.registerAdapter(TrialStatusAdapter());
    Hive.registerAdapter(UserAdapter());
    Hive.registerAdapter(NotificationSettingsAdapter());
    Hive.registerAdapter(AppSettingsAdapter());

    // Open boxes
    _trialsBox = await Hive.openBox<Trial>(_trialsBoxName);
    _userBox = await Hive.openBox<User>(_userBoxName);
    
    // Initialize SharedPreferences
    _prefs = await SharedPreferences.getInstance();
  }

  // Trial operations
  static Future<void> saveTrial(Trial trial) async {
    await _trialsBox?.put(trial.id, trial);
  }

  static Future<void> updateTrial(Trial trial) async {
    trial.updatedAt = DateTime.now();
    await _trialsBox?.put(trial.id, trial);
  }

  static Future<void> deleteTrial(String trialId) async {
    await _trialsBox?.delete(trialId);
  }

  static Trial? getTrial(String trialId) {
    return _trialsBox?.get(trialId);
  }

  static List<Trial> getAllTrials() {
    return _trialsBox?.values.toList() ?? [];
  }

  static List<Trial> getActiveTrials() {
    return getAllTrials().where((trial) => trial.status == TrialStatus.active).toList();
  }

  static List<Trial> getTrialsByStatus(TrialStatus status) {
    return getAllTrials().where((trial) => trial.status == status).toList();
  }

  static List<Trial> getExpiringTrials(int daysAhead) {
    final now = DateTime.now();
    return getActiveTrials().where((trial) {
      final daysUntilExpiry = trial.expiryDate.difference(now).inDays;
      return daysUntilExpiry <= daysAhead && daysUntilExpiry >= 0;
    }).toList();
  }

  static List<Trial> getTrialsInDateRange(DateTime start, DateTime end) {
    return getAllTrials().where((trial) {
      return trial.startDate.isAfter(start) && trial.startDate.isBefore(end);
    }).toList();
  }

  // User operations
  static Future<void> saveUser(User user) async {
    await _userBox?.put(user.id, user);
    await setCurrentUserId(user.id);
  }

  static Future<void> updateUser(User user) async {
    user.updatedAt = DateTime.now();
    await _userBox?.put(user.id, user);
  }

  static User? getCurrentUser() {
    final userId = getCurrentUserId();
    if (userId != null) {
      return _userBox?.get(userId);
    }
    return null;
  }

  static String? getCurrentUserId() {
    return _prefs?.getString(_currentUserIdKey);
  }

  static Future<void> setCurrentUserId(String userId) async {
    await _prefs?.setString(_currentUserIdKey, userId);
  }

  static Future<void> clearCurrentUser() async {
    await _prefs?.remove(_currentUserIdKey);
  }

  // App state operations
  static bool isFirstLaunch() {
    return _prefs?.getBool(_isFirstLaunchKey) ?? true;
  }

  static Future<void> setFirstLaunchCompleted() async {
    await _prefs?.setBool(_isFirstLaunchKey, false);
  }

  static bool isOnboardingCompleted() {
    return _prefs?.getBool(_onboardingCompletedKey) ?? false;
  }

  static Future<void> setOnboardingCompleted() async {
    await _prefs?.setBool(_onboardingCompletedKey, true);
  }

  // Analytics and calculations
  static double calculateMonthlySavings([DateTime? month]) {
    final targetMonth = month ?? DateTime.now();
    final startOfMonth = DateTime(targetMonth.year, targetMonth.month, 1);
    final endOfMonth = DateTime(targetMonth.year, targetMonth.month + 1, 0);

    final cancelledTrials = getAllTrials().where((trial) {
      return trial.status == TrialStatus.cancelled &&
             trial.cancelledDate != null &&
             trial.cancelledDate!.isAfter(startOfMonth) &&
             trial.cancelledDate!.isBefore(endOfMonth);
    });

    return cancelledTrials.fold(0.0, (sum, trial) => sum + (trial.moneySaved ?? 0));
  }

  static double calculateTotalSavings() {
    final cancelledTrials = getTrialsByStatus(TrialStatus.cancelled);
    return cancelledTrials.fold(0.0, (sum, trial) => sum + (trial.moneySaved ?? 0));
  }

  static Map<String, int> getTrialCountsByCategory() {
    final trials = getAllTrials();
    final Map<String, int> counts = {};
    
    for (final trial in trials) {
      counts[trial.serviceCategory] = (counts[trial.serviceCategory] ?? 0) + 1;
    }
    
    return counts;
  }

  static Map<String, double> getSavingsByCategory() {
    final cancelledTrials = getTrialsByStatus(TrialStatus.cancelled);
    final Map<String, double> savings = {};
    
    for (final trial in cancelledTrials) {
      final category = trial.serviceCategory;
      savings[category] = (savings[category] ?? 0) + (trial.moneySaved ?? 0);
    }
    
    return savings;
  }

  // Data export
  static Map<String, dynamic> exportAllData() {
    final user = getCurrentUser();
    final trials = getAllTrials();
    
    return {
      'user': user?.toJson(),
      'trials': trials.map((trial) => trial.toJson()).toList(),
      'exportDate': DateTime.now().toIso8601String(),
      'version': '1.0.0',
    };
  }

  // Data import
  static Future<bool> importData(Map<String, dynamic> data) async {
    try {
      // Clear existing data
      await _trialsBox?.clear();
      await _userBox?.clear();
      
      // Import user
      if (data['user'] != null) {
        final user = User.fromJson(data['user']);
        await saveUser(user);
      }
      
      // Import trials
      if (data['trials'] != null) {
        final trialsData = List<Map<String, dynamic>>.from(data['trials']);
        for (final trialData in trialsData) {
          final trial = Trial.fromJson(trialData);
          await saveTrial(trial);
        }
      }
      
      return true;
    } catch (e) {
      return false;
    }
  }

  // Cleanup
  static Future<void> clearAllData() async {
    await _trialsBox?.clear();
    await _userBox?.clear();
    await _prefs?.clear();
  }

  static Future<void> dispose() async {
    await _trialsBox?.close();
    await _userBox?.close();
  }
}
