import 'package:flutter/material.dart';
import '../../widgets/custom_app_bar.dart';
import '../../theme/app_theme.dart';

class AddTrialScreen extends StatelessWidget {
  const AddTrialScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Add Trial',
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: AppSpacing.md),
            
            // Placeholder content
            Padding(
              padding: const EdgeInsets.all(AppSpacing.md),
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(AppSpacing.lg),
                  child: Column(
                    children: [
                      Icon(
                        Icons.add_circle,
                        size: 64,
                        color: AppTheme.primaryGreen,
                      ),
                      const SizedBox(height: AppSpacing.md),
                      Text(
                        'Add Trial Screen',
                        style: AppTextStyles.heading2,
                      ),
                      const SizedBox(height: AppSpacing.sm),
                      Text(
                        'This will contain the trial addition interface with service selection, form inputs, and reminder settings.',
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppTheme.textSecondary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
