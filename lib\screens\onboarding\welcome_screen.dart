import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../services/storage_service.dart';
import '../../theme/app_theme.dart';

class WelcomeScreen extends StatelessWidget {
  const WelcomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.lg),
          child: Column(
            children: [
              const Spacer(),
              
              // App logo and branding
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: AppTheme.primaryGreen.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppBorderRadius.xxl),
                ),
                child: const Icon(
                  Icons.savings,
                  size: 64,
                  color: AppTheme.primaryGreen,
                ),
              ),
              
              const SizedBox(height: AppSpacing.xl),
              
              // App name
              Text(
                'Trailio',
                style: AppTextStyles.heading1.copyWith(
                  color: AppTheme.primaryGreen,
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              const SizedBox(height: AppSpacing.md),
              
              // Tagline
              Text(
                'Never miss a trial cancellation again!',
                style: AppTextStyles.bodyLarge.copyWith(
                  color: AppTheme.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: AppSpacing.xxl),
              
              // Benefits
              _buildBenefit(
                icon: Icons.notifications_active,
                title: 'Smart Reminders',
                description: 'Get notified before your trials expire',
              ),
              
              const SizedBox(height: AppSpacing.lg),
              
              _buildBenefit(
                icon: Icons.savings,
                title: 'Track Savings',
                description: 'See how much money you\'ve saved',
              ),
              
              const SizedBox(height: AppSpacing.lg),
              
              _buildBenefit(
                icon: Icons.analytics,
                title: 'Detailed Analytics',
                description: 'Understand your subscription habits',
              ),
              
              const Spacer(),
              
              // Action buttons
              Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  ElevatedButton(
                    onPressed: () async {
                      await StorageService.setFirstLaunchCompleted();
                      if (context.mounted) {
                        context.go('/onboarding');
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: AppSpacing.md),
                    ),
                    child: const Text(
                      'Get Started',
                      style: AppTextStyles.button,
                    ),
                  ),
                  
                  const SizedBox(height: AppSpacing.md),
                  
                  TextButton(
                    onPressed: () async {
                      await StorageService.setFirstLaunchCompleted();
                      await StorageService.setOnboardingCompleted();
                      if (context.mounted) {
                        context.go('/sign-in');
                      }
                    },
                    child: const Text(
                      'I already have an account',
                      style: AppTextStyles.button,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: AppSpacing.lg),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBenefit({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Row(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: AppTheme.primaryGreen.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppBorderRadius.lg),
          ),
          child: Icon(
            icon,
            color: AppTheme.primaryGreen,
            size: 24,
          ),
        ),
        
        const SizedBox(width: AppSpacing.md),
        
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextStyles.heading3.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: AppSpacing.xs),
              Text(
                description,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppTheme.textSecondary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
