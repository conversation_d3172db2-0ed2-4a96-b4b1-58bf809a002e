import '../models/trial.dart';
import '../models/user.dart';
import 'storage_service.dart';
import 'notification_service.dart';

class TrialService {
  static Future<Trial> createTrial({
    required String serviceName,
    required String serviceCategory,
    String? logoUrl,
    required DateTime startDate,
    required int trialDurationDays,
    required double monthlyCost,
    String currency = 'USD',
    List<int>? reminderDaysBeforeExpiry,
    List<String>? notificationTypes,
    String? notes,
  }) async {
    final trial = Trial(
      serviceName: serviceName,
      serviceCategory: serviceCategory,
      logoUrl: logoUrl,
      startDate: startDate,
      trialDurationDays: trialDurationDays,
      monthlyCost: monthlyCost,
      currency: currency,
      reminderDaysBeforeExpiry: reminderDaysBeforeExpiry,
      notificationTypes: notificationTypes,
      notes: notes,
    );

    await StorageService.saveTrial(trial);
    await NotificationService.scheduleTrialReminders(trial);

    return trial;
  }

  static Future<void> updateTrial(Trial trial) async {
    await StorageService.updateTrial(trial);
    
    // Reschedule notifications if the trial is still active
    if (trial.status == TrialStatus.active) {
      await NotificationService.scheduleTrialReminders(trial);
    } else {
      await NotificationService.cancelTrialNotifications(trial.id);
    }
  }

  static Future<void> cancelTrial(String trialId) async {
    final trial = StorageService.getTrial(trialId);
    if (trial == null) return;

    trial.cancel();
    await StorageService.updateTrial(trial);
    await NotificationService.cancelTrialNotifications(trial.id);
    await NotificationService.showTrialCancelledCelebration(trial);

    // Check if user achieved their savings goal
    await _checkSavingsGoal();
  }

  static Future<void> markTrialAsConverted(String trialId) async {
    final trial = StorageService.getTrial(trialId);
    if (trial == null) return;

    trial.markAsConverted();
    await StorageService.updateTrial(trial);
    await NotificationService.cancelTrialNotifications(trial.id);
  }

  static Future<void> markTrialAsExpired(String trialId) async {
    final trial = StorageService.getTrial(trialId);
    if (trial == null) return;

    trial.markAsExpired();
    await StorageService.updateTrial(trial);
    await NotificationService.cancelTrialNotifications(trial.id);
  }

  static Future<void> deleteTrial(String trialId) async {
    await NotificationService.cancelTrialNotifications(trialId);
    await StorageService.deleteTrial(trialId);
  }

  static List<Trial> getActiveTrials() {
    return StorageService.getActiveTrials();
  }

  static List<Trial> getExpiringTrials(int daysAhead) {
    return StorageService.getExpiringTrials(daysAhead);
  }

  static List<Trial> getTrialsByCategory(String category) {
    return StorageService.getAllTrials()
        .where((trial) => trial.serviceCategory == category)
        .toList();
  }

  static List<Trial> getTrialsByStatus(TrialStatus status) {
    return StorageService.getTrialsByStatus(status);
  }

  static List<Trial> getTrialsInDateRange(DateTime start, DateTime end) {
    return StorageService.getTrialsInDateRange(start, end);
  }

  static Future<void> processExpiredTrials() async {
    final activeTrials = getActiveTrials();
    final now = DateTime.now();

    for (final trial in activeTrials) {
      if (trial.expiryDate.isBefore(now)) {
        await markTrialAsExpired(trial.id);
      }
    }
  }

  static TrialStats getTrialStats() {
    final allTrials = StorageService.getAllTrials();
    final activeTrials = allTrials.where((t) => t.status == TrialStatus.active).toList();
    final cancelledTrials = allTrials.where((t) => t.status == TrialStatus.cancelled).toList();
    final expiredTrials = allTrials.where((t) => t.status == TrialStatus.expired).toList();
    final convertedTrials = allTrials.where((t) => t.status == TrialStatus.converted).toList();

    final totalSavings = cancelledTrials.fold(0.0, (sum, trial) => sum + (trial.moneySaved ?? 0));
    final potentialMonthlyCost = activeTrials.fold(0.0, (sum, trial) => sum + trial.monthlyCost);

    final expiringThisWeek = getExpiringTrials(7);
    final expiringToday = getExpiringTrials(0);

    return TrialStats(
      totalTrials: allTrials.length,
      activeTrials: activeTrials.length,
      cancelledTrials: cancelledTrials.length,
      expiredTrials: expiredTrials.length,
      convertedTrials: convertedTrials.length,
      totalSavings: totalSavings,
      potentialMonthlyCost: potentialMonthlyCost,
      expiringThisWeek: expiringThisWeek.length,
      expiringToday: expiringToday.length,
      monthlySavings: StorageService.calculateMonthlySavings(),
    );
  }

  static Map<String, double> getSavingsByCategory() {
    return StorageService.getSavingsByCategory();
  }

  static Map<String, int> getTrialCountsByCategory() {
    return StorageService.getTrialCountsByCategory();
  }

  static List<MonthlyData> getMonthlySavingsData(int months) {
    final List<MonthlyData> data = [];
    final now = DateTime.now();

    for (int i = months - 1; i >= 0; i--) {
      final month = DateTime(now.year, now.month - i, 1);
      final savings = StorageService.calculateMonthlySavings(month);
      data.add(MonthlyData(
        month: month,
        savings: savings,
      ));
    }

    return data;
  }

  static Future<void> _checkSavingsGoal() async {
    final user = StorageService.getCurrentUser();
    if (user == null) return;

    final monthlySavings = StorageService.calculateMonthlySavings();
    if (monthlySavings >= user.monthlySavingsGoal) {
      await NotificationService.showSavingsGoalAchieved(user.monthlySavingsGoal);
    }
  }

  static Future<Map<String, dynamic>> exportTrialData() async {
    return StorageService.exportAllData();
  }

  static Future<bool> importTrialData(Map<String, dynamic> data) async {
    final success = await StorageService.importData(data);
    if (success) {
      // Reschedule all notifications for imported active trials
      await NotificationService.scheduleAllActiveTrialReminders();
    }
    return success;
  }

  static Future<void> updateReminderSettings(String trialId, List<int> reminderDays) async {
    final trial = StorageService.getTrial(trialId);
    if (trial == null) return;

    trial.reminderDaysBeforeExpiry = reminderDays;
    await updateTrial(trial);
  }

  static Future<void> snoozeTrialReminder(String trialId, int snoozeDays) async {
    final trial = StorageService.getTrial(trialId);
    if (trial == null) return;

    // Cancel current notifications
    await NotificationService.cancelTrialNotifications(trial.id);

    // Create a temporary reminder for the snooze period
    final snoozeDate = DateTime.now().add(Duration(days: snoozeDays));
    
    await NotificationService.showImmediateNotification(
      title: '😴 Reminder Snoozed',
      body: '${trial.serviceName} reminder snoozed for $snoozeDays days',
      payload: trial.id,
    );

    // Reschedule original reminders (they will be filtered out if in the past)
    await NotificationService.scheduleTrialReminders(trial);
  }
}

class TrialStats {
  final int totalTrials;
  final int activeTrials;
  final int cancelledTrials;
  final int expiredTrials;
  final int convertedTrials;
  final double totalSavings;
  final double potentialMonthlyCost;
  final int expiringThisWeek;
  final int expiringToday;
  final double monthlySavings;

  TrialStats({
    required this.totalTrials,
    required this.activeTrials,
    required this.cancelledTrials,
    required this.expiredTrials,
    required this.convertedTrials,
    required this.totalSavings,
    required this.potentialMonthlyCost,
    required this.expiringThisWeek,
    required this.expiringToday,
    required this.monthlySavings,
  });
}

class MonthlyData {
  final DateTime month;
  final double savings;

  MonthlyData({
    required this.month,
    required this.savings,
  });
}
